name: Deploy to Production

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - staging
          - production
      version:
        description: 'Version to deploy (leave empty for latest)'
        required: false
        type: string
  
  workflow_run:
    workflows: ["CI/CD Pipeline"]
    types:
      - completed
    branches: [main]

env:
  NODE_VERSION: '20.x'

jobs:
  debug-info:
    runs-on: ubuntu-latest
    steps:
    - name: Debug workflow inputs
      run: |
        echo "Event name: ${{ github.event_name }}"
        echo "Environment input: ${{ github.event.inputs.environment }}"
        echo "Ref: ${{ github.ref }}"
        echo "Repository: ${{ github.repository }}"
  deploy-staging:
    if: |
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --production

    - name: Create environment file
      run: |
        echo "PORT=${{ secrets.STAGING_PORT }}" > .env
        echo "MONGODB_URI=${{ secrets.STAGING_MONGODB_URI }}" >> .env
        echo "JWT_SECRET=${{ secrets.STAGING_JWT_SECRET }}" >> .env
        echo "NODE_ENV=staging" >> .env

    # Example deployment to different platforms - uncomment the one you need

    # Deploy to Heroku
    # - name: Deploy to Heroku Staging
    #   uses: akhileshns/heroku-deploy@v3.12.14
    #   with:
    #     heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
    #     heroku_app_name: ${{ secrets.HEROKU_STAGING_APP_NAME }}
    #     heroku_email: ${{ secrets.HEROKU_EMAIL }}

    # Deploy to Railway
    # - name: Deploy to Railway Staging
    #   run: |
    #     npm install -g @railway/cli
    #     railway login --token ${{ secrets.RAILWAY_TOKEN }}
    #     railway deploy --service ${{ secrets.RAILWAY_STAGING_SERVICE }}

    # Deploy to DigitalOcean App Platform
    # - name: Deploy to DigitalOcean Staging
    #   uses: digitalocean/app_action@v1.1.5
    #   with:
    #     app_name: ${{ secrets.DO_STAGING_APP_NAME }}
    #     token: ${{ secrets.DO_ACCESS_TOKEN }}

    # Deploy to AWS Elastic Beanstalk
    # - name: Deploy to AWS EB Staging
    #   uses: einaregilsson/beanstalk-deploy@v21
    #   with:
    #     aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #     aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #     application_name: ${{ secrets.AWS_EB_APP_NAME }}
    #     environment_name: ${{ secrets.AWS_EB_STAGING_ENV }}
    #     region: ${{ secrets.AWS_REGION }}
    #     version_label: ${{ github.sha }}

    # Deploy via SSH
    # - name: Deploy via SSH to Staging
    #   uses: appleboy/ssh-action@v1.0.0
    #   with:
    #     host: ${{ secrets.STAGING_HOST }}
    #     username: ${{ secrets.STAGING_USERNAME }}
    #     key: ${{ secrets.STAGING_SSH_KEY }}
    #     port: ${{ secrets.STAGING_PORT || 22 }}
    #     script: |
    #       cd /path/to/your/app
    #       git pull origin main
    #       npm ci --production
    #       pm2 restart weplace-api-staging

    - name: Deployment Success Notification
      run: |
        echo "✅ Successfully deployed to staging environment"
        echo "🚀 Application URL: ${{ secrets.STAGING_URL }}"

  deploy-production:
    if: |
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: Checkout latest code from main
      uses: actions/checkout@v4
      with:
        ref: main
        fetch-depth: 0

    - name: Verify we're on latest main
      run: |
        echo "Current commit: $(git rev-parse HEAD)"
        echo "Latest main commit: $(git rev-parse origin/main)"
        if [ "$(git rev-parse HEAD)" != "$(git rev-parse origin/main)" ]; then
          echo "❌ Not on latest main branch!"
          exit 1
        fi
        echo "✅ Confirmed: Using latest main branch"

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --production

    - name: Check for pending changes
      run: |
        echo "🔍 Checking for any uncommitted changes..."
        if [ -n "$(git status --porcelain)" ]; then
          echo "❌ There are uncommitted changes!"
          git status
          exit 1
        fi
        echo "✅ No uncommitted changes found"

    - name: Verify main branch is ahead of production tag
      run: |
        # Get the latest production tag (if exists)
        LATEST_TAG=$(git tag -l "v*" --sort=-version:refname | head -n1)
        if [ -n "$LATEST_TAG" ]; then
          echo "Latest production tag: $LATEST_TAG"
          COMMITS_AHEAD=$(git rev-list --count $LATEST_TAG..HEAD)
          echo "Commits ahead of latest tag: $COMMITS_AHEAD"
          if [ "$COMMITS_AHEAD" -eq 0 ]; then
            echo "⚠️  Warning: No new commits since last production deployment"
          fi
        else
          echo "No previous production tags found - this appears to be first deployment"
        fi

    - name: Create environment file
      run: |
        echo "PORT=${{ secrets.PRODUCTION_PORT }}" > .env
        echo "MONGODB_URI=${{ secrets.PRODUCTION_MONGODB_URI }}" >> .env
        echo "JWT_SECRET=${{ secrets.PRODUCTION_JWT_SECRET }}" >> .env
        echo "NODE_ENV=production" >> .env

    - name: Run production health check
      run: |
        timeout 30s npm start &
        sleep 10
        curl -f http://localhost:${{ secrets.PRODUCTION_PORT || 3000 }}/health || exit 1
        pkill -f "node server.js" || true

    # Example deployment to different platforms - uncomment the one you need

    # Deploy to Heroku
    # - name: Deploy to Heroku Production
    #   uses: akhileshns/heroku-deploy@v3.12.14
    #   with:
    #     heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
    #     heroku_app_name: ${{ secrets.HEROKU_PRODUCTION_APP_NAME }}
    #     heroku_email: ${{ secrets.HEROKU_EMAIL }}

    # Deploy to Railway
    # - name: Deploy to Railway Production
    #   run: |
    #     npm install -g @railway/cli
    #     railway login --token ${{ secrets.RAILWAY_TOKEN }}
    #     railway deploy --service ${{ secrets.RAILWAY_PRODUCTION_SERVICE }}

    # Deploy to DigitalOcean App Platform
    # - name: Deploy to DigitalOcean Production
    #   uses: digitalocean/app_action@v1.1.5
    #   with:
    #     app_name: ${{ secrets.DO_PRODUCTION_APP_NAME }}
    #     token: ${{ secrets.DO_ACCESS_TOKEN }}

    # Deploy to AWS Elastic Beanstalk
    # - name: Deploy to AWS EB Production
    #   uses: einaregilsson/beanstalk-deploy@v21
    #   with:
    #     aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #     aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #     application_name: ${{ secrets.AWS_EB_APP_NAME }}
    #     environment_name: ${{ secrets.AWS_EB_PRODUCTION_ENV }}
    #     region: ${{ secrets.AWS_REGION }}
    #     version_label: ${{ github.sha }}

    # Deploy via SSH
    # - name: Deploy via SSH to Production
    #   uses: appleboy/ssh-action@v1.0.0
    #   with:
    #     host: ${{ secrets.PRODUCTION_HOST }}
    #     username: ${{ secrets.PRODUCTION_USERNAME }}
    #     key: ${{ secrets.PRODUCTION_SSH_KEY }}
    #     port: ${{ secrets.PRODUCTION_PORT || 22 }}
    #     script: |
    #       cd /path/to/your/app
    #       git pull origin main
    #       npm ci --production
    #       pm2 restart weplace-api-production

    - name: Create production deployment tag
      run: |
        # Create a tag for this production deployment
        TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
        TAG_NAME="prod-${TIMESTAMP}-${GITHUB_SHA:0:7}"
        git config user.name "GitHub Actions"
        git config user.email "<EMAIL>"
        git tag -a "$TAG_NAME" -m "Production deployment on $TIMESTAMP"
        git push origin "$TAG_NAME"
        echo "✅ Created production tag: $TAG_NAME"

    - name: Deployment Success Notification
      run: |
        echo "✅ Successfully deployed to production environment"
        echo "🚀 Application URL: ${{ secrets.PRODUCTION_URL }}"
        echo "📋 Deployment commit: ${GITHUB_SHA}"
        echo "🏷️  Production tag: prod-$(date +"%Y%m%d-%H%M%S")-${GITHUB_SHA:0:7}"

  rollback:
    if: failure()
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    
    steps:
    - name: Rollback Notification
      run: |
        echo "❌ Deployment failed. Consider rolling back to previous version."
        echo "🔄 Manual rollback may be required."

  post-deploy-tests:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: success()
    
    steps:
    - name: Run smoke tests
      run: |
        echo "🧪 Running post-deployment smoke tests..."
        # Add your smoke tests here
        # curl -f ${{ secrets.PRODUCTION_URL }}/api/v1/health
        # curl -f ${{ secrets.STAGING_URL }}/api/v1/health

    - name: Notify deployment success
      run: |
        echo "🎉 Deployment completed successfully!"
        echo "✅ All smoke tests passed"
