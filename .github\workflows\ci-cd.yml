name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    services:
      mongodb:
        image: mongo:6.0
        env:
          MONGO_INITDB_ROOT_USERNAME: root
          MONGO_INITDB_ROOT_PASSWORD: password
        ports:
          - 27017:27017
        options: >-
          --health-cmd mongo
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Create test environment file
      run: |
        echo "PORT=3001" > .env.test
        echo "MONGODB_URI=*********************************************************************" >> .env.test
        echo "JWT_SECRET=test_jwt_secret_key_for_testing" >> .env.test
        echo "NODE_ENV=test" >> .env.test

    - name: Wait for MongoDB
      run: |
        until nc -z localhost 27017; do
          echo "Waiting for MongoDB..."
          sleep 2
        done

    - name: Run tests
      run: npm test
      env:
        NODE_ENV: test

    - name: Run linting (if available)
      run: |
        if npm list eslint --depth=0 2>/dev/null; then
          npm run lint
        else
          echo "ESLint not found, skipping linting"
        fi
      continue-on-error: true

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run security audit
      run: npm audit --audit-level=moderate

    - name: Check for vulnerabilities
      run: npm audit --audit-level=high --production

  build:
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --production

    - name: Create production build artifact
      run: |
        mkdir -p build
        cp -r src build/
        cp package*.json build/
        cp server.js build/
        cp app.js build/

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: production-build
        path: build/
        retention-days: 30

  # Uncomment and configure this job when you're ready to deploy
  # deploy:
  #   runs-on: ubuntu-latest
  #   needs: build
  #   if: github.ref == 'refs/heads/main'
  #   environment: production
  #   
  #   steps:
  #   - name: Download build artifacts
  #     uses: actions/download-artifact@v4
  #     with:
  #       name: production-build
  #       path: ./build
  #   
  #   - name: Deploy to production
  #     run: |
  #       echo "Add your deployment commands here"
  #       # Example for different platforms:
  #       # - Heroku: Use heroku/deploy-via-git action
  #       # - AWS: Use aws-actions/configure-aws-credentials
  #       # - DigitalOcean: Use digitalocean/action-doctl
  #       # - Railway: Use railway deploy command
  #       # - Vercel: Use vercel/action
